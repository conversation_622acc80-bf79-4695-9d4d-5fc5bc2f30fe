/* 2025 Complete Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Ensure no gaps above header */
html, body {
    margin: 0;
    padding: 0;
}

html {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

:root {
    /* Color Palette */
    --primary-green: #2E7D32;
    --secondary-green: #4CAF50;
    --accent-orange: #FF6B35;
    --dark-blue: #1565C0;
    --light-blue: #E3F2FD;
    --neutral-dark: #212121;
    --neutral-medium: #757575;
    --neutral-light: #F5F5F5;
    --white: #FFFFFF;
    --success: #4CAF50;
    --warning: #FF9800;
    --error: #F44336;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Mobile-First Touch Targets */
    --touch-target-min: 44px;
    --touch-target-comfortable: 48px;
    --touch-target-large: 56px;

    /* Mobile Typography Scale */
    --mobile-font-xs: 12px;
    --mobile-font-sm: 14px;
    --mobile-font-base: 16px;
    --mobile-font-lg: 18px;
    --mobile-font-xl: 20px;
    --mobile-font-2xl: 22px;
    --mobile-font-3xl: 26px;
    --mobile-font-4xl: 30px;
}

/* Mobile Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for content areas */
p, h1, h2, h3, h4, h5, h6, span, div.content, .product-description, .form-input, .form-textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Smooth scrolling for mobile */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Optimize images for mobile */
img {
    max-width: 100%;
    height: auto;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

body {
    font-family: var(--font-family);
    font-size: var(--mobile-font-base);
    line-height: 1.6;
    color: var(--neutral-dark);
    background-color: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    touch-action: manipulation;
    /* Optimized padding-top to prevent content overlap with sticky header */
    padding-top: 100px; /* Reduced for more compact layout */
}

/* Prevent body padding when mobile menu is open */
body.menu-open {
    overflow: hidden;
    padding-top: 0;
}

/* Desktop font size override */
@media (min-width: 768px) {
    body {
        font-size: var(--font-size-base);
        padding-top: 110px; /* Optimized padding for desktop header */
    }
}

/* Optimized mobile responsive header padding adjustments */
@media (max-width: 320px) {
    body {
        padding-top: 85px; /* Minimal padding for very small screens */
    }
}

@media (min-width: 321px) and (max-width: 480px) {
    body {
        padding-top: 90px; /* Smaller padding for small screens */
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    body {
        padding-top: 95px; /* Medium padding for medium mobile screens */
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    body {
        padding-top: 105px; /* Tablet padding */
    }
}

@media (min-width: 1024px) {
    body {
        padding-top: 110px; /* Desktop padding */
    }
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}

/* Mobile-First Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

/* Mobile typography scale */
h1 { font-size: var(--mobile-font-4xl); }
h2 { font-size: var(--mobile-font-3xl); }
h3 { font-size: var(--mobile-font-2xl); }
h4 { font-size: var(--mobile-font-xl); }
h5 { font-size: var(--mobile-font-lg); }
h6 { font-size: var(--mobile-font-base); }

/* Desktop typography scale */
@media (min-width: 768px) {
    h1 { font-size: var(--font-size-4xl); }
    h2 { font-size: var(--font-size-3xl); }
    h3 { font-size: var(--font-size-2xl); }
    h4 { font-size: var(--font-size-xl); }
    h5 { font-size: var(--font-size-lg); }
    h6 { font-size: var(--font-size-base); }
}

p {
    margin-bottom: var(--spacing-md);
    color: var(--neutral-medium);
}

/* Mobile-First Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    font-size: var(--mobile-font-base);
    font-weight: 500;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: var(--touch-target-comfortable);
    min-width: var(--touch-target-comfortable);
    gap: var(--spacing-sm);
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

/* Desktop button adjustments */
@media (min-width: 768px) {
    .btn {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-base);
    }
}

.btn-primary {
    background-color: var(--accent-orange);
    color: var(--white);
    border-color: var(--accent-orange);
}

.btn-primary:hover {
    background-color: #E55A2B;
    border-color: #E55A2B;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-secondary:hover {
    background-color: var(--primary-green);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    color: var(--neutral-dark);
    border-color: var(--neutral-medium);
}

.btn-outline:hover {
    background-color: var(--neutral-dark);
    color: var(--white);
    border-color: var(--neutral-dark);
}

/* 2025 Modern Header - Flush Against Viewport */
.modern-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: fixed; /* Changed to fixed for better control */
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid rgba(46, 125, 50, 0.1);
    margin: 0;
    padding: 0;
}

/* Mobile-First Top Bar */
.top-bar {
    background: linear-gradient(135deg, var(--primary-green) 0%, #2e7d32 100%);
    color: white;
    padding: 6px 0;
    font-size: var(--mobile-font-sm);
}

/* Desktop top bar adjustments */
@media (min-width: 768px) {
    .top-bar {
        padding: 8px 0;
        font-size: 14px;
    }
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.marketing-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.marketing-tag i {
    color: var(--accent-orange);
}

.top-bar-right {
    display: flex;
    gap: 20px;
}

.top-contact {
    display: flex;
    align-items: center;
    gap: 6px;
    color: white;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.3s ease;
}

.top-contact:hover {
    color: var(--accent-orange);
}

/* Mobile-First Modern Navbar */
.modern-navbar {
    padding: 12px 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Desktop navbar adjustments */
@media (min-width: 768px) {
    .modern-navbar {
        padding: 16px 0;
    }
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: nowrap;
    min-height: 60px;
}

/* Modern Logo */
.modern-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.modern-logo:hover {
    transform: translateY(-2px);
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-green) 0%, #2e7d32 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-green);
    line-height: 1;
}

.brand-tagline {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-top: 2px;
}

/* Modern Navigation */
.modern-nav-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
}

.modern-nav-menu .nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 4px;
    align-items: center;
    white-space: nowrap;
}

.modern-nav-menu .nav-item {
    position: relative;
    flex-shrink: 0;
}

.modern-nav-menu .nav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 16px;
    text-decoration: none;
    color: var(--neutral-dark);
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    font-size: var(--mobile-font-sm);
    white-space: nowrap;
    min-height: var(--touch-target-min);
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Desktop nav link adjustments */
@media (min-width: 768px) {
    .modern-nav-menu .nav-link {
        padding: 10px 14px;
        font-size: 14px;
    }
}

.modern-nav-menu .nav-link:hover {
    background: rgba(46, 125, 50, 0.1);
    color: var(--primary-green);
    transform: translateY(-1px);
}

.modern-nav-menu .nav-link.active {
    background: var(--primary-green);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.modern-nav-menu .nav-link i {
    font-size: 14px;
    flex-shrink: 0;
}

.dropdown-arrow {
    font-size: 12px !important;
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* Modern Dropdown */
.modern-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    min-width: 600px;
    border: 1px solid rgba(46, 125, 50, 0.1);
}

.dropdown:hover .modern-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 24px;
}

.dropdown-section h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-green);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(46, 125, 50, 0.1);
}

.dropdown-section a {
    display: block;
    padding: 8px 12px;
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.dropdown-section a:hover {
    background: rgba(46, 125, 50, 0.1);
    color: var(--primary-green);
    transform: translateX(4px);
}

/* Modern Search */
.modern-search {
    position: relative;
    margin-right: 12px;
    flex-shrink: 0;
}

.modern-search .search-input {
    width: 180px;
    padding: 12px 16px 12px 40px;
    border: 2px solid rgba(46, 125, 50, 0.2);
    border-radius: 20px;
    font-size: var(--mobile-font-base);
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    min-height: var(--touch-target-min);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Desktop search input adjustments */
@media (min-width: 768px) {
    .modern-search .search-input {
        width: 220px;
        padding: 10px 14px 10px 36px;
        font-size: 13px;
    }
}

.modern-search .search-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
    width: 260px;
}

.modern-search .search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--primary-green);
    font-size: 14px;
    cursor: pointer;
}

/* Mobile-First CTA Button */
.cta-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 18px;
    background: linear-gradient(135deg, var(--accent-orange) 0%, #f57c00 100%);
    color: white;
    text-decoration: none;
    border-radius: 20px;
    font-weight: 600;
    font-size: var(--mobile-font-sm);
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(255, 152, 0, 0.3);
    white-space: nowrap;
    flex-shrink: 0;
    min-height: var(--touch-target-min);
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Desktop CTA button adjustments */
@media (min-width: 768px) {
    .cta-button {
        padding: 10px 16px;
        font-size: 13px;
    }
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

/* Modern Mobile Toggle */
.modern-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
}

.toggle-line {
    width: 24px;
    height: 3px;
    background: var(--primary-green);
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Nav Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    min-width: 280px;
    justify-content: flex-end;
}

/* Marketing Info Card Styles */
.marketing-info {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid rgba(46, 125, 50, 0.1);
}

.marketing-details {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.marketing-details i {
    color: var(--accent-orange);
    font-size: 20px;
    margin-top: 4px;
}

.company-name {
    color: var(--primary-green);
    font-weight: 700;
    font-size: 16px;
    display: block;
    margin: 4px 0;
}

.company-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.partnership-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.partnership-badges .badge {
    background: var(--primary-green);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Footer Marketing Credit */
.marketing-credit {
    color: #666;
    font-size: 14px;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(46, 125, 50, 0.1);
}

.marketing-credit strong {
    color: var(--primary-green);
}

/* Additional Header Fixes */
.modern-header {
    position: relative;
    overflow: visible;
}

.modern-navbar .container {
    position: relative;
}

/* Ensure proper z-index stacking */
.modern-nav-menu {
    z-index: 100;
}

.modern-dropdown {
    z-index: 200;
}

.modern-nav-menu.active {
    z-index: 150;
}

/* Fix for overlapping text */
.modern-nav-menu .nav-link span {
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Ensure mobile toggle is always visible */
.modern-toggle {
    z-index: 300;
    position: relative;
}

/* Fix dropdown positioning */
.modern-nav-menu .dropdown {
    position: relative;
}

.modern-nav-menu .dropdown .modern-dropdown {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
}

/* Modern Header Responsive */
@media (max-width: 1200px) {
    .navbar-content {
        gap: 15px;
    }

    .modern-nav-menu {
        max-width: 500px;
    }

    .modern-nav-menu .nav-link {
        padding: 8px 12px;
        font-size: 13px;
        gap: 4px;
    }

    .modern-search .search-input {
        width: 180px;
    }

    .modern-search .search-input:focus {
        width: 220px;
    }

    .nav-actions {
        min-width: 240px;
    }
}

@media (max-width: 1024px) {
    .modern-dropdown {
        min-width: 500px;
    }

    .dropdown-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .modern-nav-menu .nav-link {
        padding: 8px 10px;
        font-size: 12px;
    }

    .modern-nav-menu .nav-link span {
        display: none;
    }

    .modern-nav-menu .nav-link i {
        font-size: 16px;
    }

    .modern-search .search-input {
        width: 160px;
    }

    .modern-search .search-input:focus {
        width: 200px;
    }

    .nav-actions {
        min-width: 200px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        padding: 6px 0;
    }

    .top-bar-content {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .top-bar-right {
        gap: 16px;
    }

    .modern-navbar {
        padding: 12px 0;
        position: relative;
    }

    .navbar-content {
        flex-wrap: nowrap;
        position: relative;
    }

    .brand-name {
        font-size: 18px;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .modern-nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 12px 12px;
        z-index: 1000;
        max-width: none;
    }

    .modern-nav-menu.active {
        display: block;
    }

    .modern-nav-menu .nav-links {
        flex-direction: column;
        padding: 20px;
        gap: 8px;
    }

    .modern-nav-menu .nav-link {
        justify-content: flex-start;
        padding: 14px 16px;
        border-radius: 8px;
        font-size: 14px;
    }

    .modern-nav-menu .nav-link span {
        display: inline;
    }

    .modern-dropdown {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        min-width: auto;
        margin-top: 8px;
        background: rgba(46, 125, 50, 0.05);
    }

    .dropdown-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 16px;
    }

    .nav-actions {
        order: -1;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 12px;
        min-width: auto;
    }

    .modern-search {
        margin-right: 12px;
        flex: 1;
    }

    .modern-search .search-input {
        width: 100%;
        min-width: 0;
    }

    .modern-search .search-input:focus {
        width: 100%;
    }

    .modern-toggle {
        display: flex;
    }

    .cta-button {
        padding: 8px 12px;
        font-size: 12px;
        flex-shrink: 0;
    }
}

@media (max-width: 480px) {
    .top-bar-right {
        flex-direction: column;
        gap: 8px;
    }

    .marketing-tag {
        font-size: 11px;
        text-align: center;
    }

    .brand-name {
        font-size: 16px;
    }

    .brand-tagline {
        font-size: 10px;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .navbar-content {
        gap: 8px;
    }

    .nav-actions {
        flex-direction: row;
        gap: 8px;
        width: 100%;
        justify-content: space-between;
    }

    .modern-search {
        margin-right: 0;
        flex: 1;
        max-width: 150px;
    }

    .modern-search .search-input {
        font-size: 12px;
        padding: 8px 12px 8px 32px;
    }

    .cta-button {
        padding: 8px 10px;
        font-size: 11px;
        gap: 4px;
    }

    .cta-button span {
        display: none;
    }

    .partnership-badges {
        justify-content: center;
    }

    /* Ensure mobile menu doesn't overlap */
    .modern-nav-menu.active {
        margin-top: 8px;
    }
}

.navbar {
    padding: var(--spacing-md) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--primary-green);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.nav-brand .logo i {
    font-size: var(--font-size-2xl);
}

/* 2025 Ultra-Modern Mobile Menu Toggle */
.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3px;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%);
    border: 1px solid rgba(46, 125, 50, 0.2);
    border-radius: 12px;
    cursor: pointer;
    padding: 8px;
    z-index: 1001;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    backdrop-filter: blur(8px) saturate(1.2);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.mobile-menu-toggle:hover {
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.1) 0%,
        rgba(46, 125, 50, 0.05) 100%);
    border-color: rgba(46, 125, 50, 0.3);
    transform: translateY(-1px);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background-color: var(--neutral-dark);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 2px;
}

.mobile-menu-toggle.active {
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.15) 0%,
        rgba(46, 125, 50, 0.08) 100%);
    border-color: var(--primary-green);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background-color: var(--primary-green);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background-color: var(--primary-green);
}

/* 2025 Ultra-Modern Mobile Navigation Menu */
.nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 255, 254, 0.9) 100%);
    backdrop-filter: blur(20px) saturate(1.3);
    -webkit-backdrop-filter: blur(20px) saturate(1.3);
    display: flex;
    flex-direction: column;
    padding: 80px 24px 24px 24px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 999;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: -10px 0 40px rgba(0, 0, 0, 0.1);
}

.nav-menu.active {
    right: 0;
}

/* 2025 Modern Mobile Navigation Links */
.nav-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 32px;
    padding: 0;
}

.nav-links a {
    text-decoration: none;
    color: var(--neutral-dark);
    font-weight: 600;
    font-size: 18px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-radius: 12px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(255, 255, 255, 0.3) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    min-height: 56px; /* Ensure 44px+ touch target */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-green);
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.15) 0%,
        rgba(46, 125, 50, 0.08) 100%);
    border-color: rgba(46, 125, 50, 0.3);
    transform: translateX(8px);
    box-shadow:
        0 4px 16px rgba(46, 125, 50, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.nav-links a i {
    font-size: 16px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.nav-links a:hover i,
.nav-links a.active i {
    opacity: 1;
    transform: scale(1.1);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    min-width: 600px;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    display: flex;
    gap: var(--spacing-xl);
}

.dropdown-section h4 {
    color: var(--primary-green);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-md);
}

.dropdown-section a {
    display: block;
    padding: var(--spacing-sm) 0;
    color: var(--neutral-medium);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.dropdown-section a:hover {
    color: var(--primary-green);
    padding-left: var(--spacing-sm);
}

/* Search Container */
.search-container {
    position: relative;
    margin: var(--spacing-lg) 0;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md);
    padding-right: 50px;
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--neutral-medium);
    cursor: pointer;
    padding: var(--spacing-sm);
    transition: var(--transition-fast);
}

.search-btn:hover {
    color: var(--primary-green);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--neutral-light);
    border-top: none;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    box-shadow: var(--shadow-md);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestions.active {
    display: block;
}

.suggestion-item {
    padding: var(--spacing-md);
    cursor: pointer;
    border-bottom: 1px solid var(--neutral-light);
    transition: var(--transition-fast);
}

.suggestion-item:hover {
    background-color: var(--light-blue);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Header Contact */
.header-contact {
    margin-top: var(--spacing-lg);
}

.toll-free {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    color: var(--accent-orange);
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.toll-free:hover {
    color: #E55A2B;
}

/* 2025 Mobile Navigation Responsive Behavior */
@media (max-width: 767px) {
    /* Hide desktop navigation on mobile */
    .modern-nav-menu {
        display: none;
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
    }

    /* Mobile navigation overlay */
    .nav-menu {
        display: flex;
    }

    /* Hide search and CTA on mobile */
    .nav-actions {
        display: none;
    }
}

/* 2025 Desktop Navigation */
@media (min-width: 768px) {
    /* Hide mobile menu toggle on desktop */
    .mobile-menu-toggle {
        display: none;
    }

    /* Hide mobile navigation menu on desktop */
    .nav-menu {
        display: none;
    }

    /* Show desktop navigation */
    .modern-nav-menu {
        display: flex;
        position: static;
        width: auto;
        height: auto;
        background: none;
        flex-direction: row;
        align-items: center;
        gap: 0;
        padding: 0;
        backdrop-filter: none;
        box-shadow: none;
        border: none;
    }

    .modern-nav-menu .nav-links {
        flex-direction: row;
        gap: 4px;
        margin-bottom: 0;
        padding: 0;
    }

    .modern-nav-menu .nav-links a {
        font-size: 14px;
        padding: 10px 14px;
        background: none;
        border: none;
        backdrop-filter: none;
        min-height: auto;
        border-radius: 8px;
    }

    .modern-nav-menu .nav-links a:hover,
    .modern-nav-menu .nav-links a.active {
        background: rgba(46, 125, 50, 0.1);
        transform: translateY(-1px);
        box-shadow: none;
    }

    .modern-nav-menu .nav-links a.active {
        background: var(--primary-green);
        color: white;
        box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
    }

    /* Show search and actions on desktop */
    .nav-actions {
        display: flex;
    }

    .search-container {
        margin: 0;
        width: 300px;
    }

    .header-contact {
        margin-top: 0;
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--light-blue) 0%, var(--white) 100%);
    padding: var(--spacing-3xl) 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-content .container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-lg);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

@media (min-width: 768px) {
    .hero-content .container {
        grid-template-columns: 1fr 1fr;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-cta {
        flex-direction: row;
    }
}

/* Trust Bar */
.trust-bar {
    background-color: var(--neutral-light);
    padding: var(--spacing-xl) 0;
    text-align: center;
}

.trust-text {
    font-size: var(--font-size-sm);
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-lg);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.brand-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.brand-logos img {
    height: 40px;
    width: auto;
    opacity: 0.7;
    transition: var(--transition-fast);
    filter: grayscale(100%);
}

.brand-logos img:hover {
    opacity: 1;
    filter: grayscale(0%);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--neutral-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Categories Section */
.categories {
    padding: var(--spacing-3xl) 0;
}

.category-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .category-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .category-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.category-card {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    text-decoration: none;
    color: inherit;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: var(--transition-normal);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.category-card h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-sm);
}

.category-card p {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.category-count {
    display: inline-block;
    background-color: var(--light-blue);
    color: var(--dark-blue);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Featured Products Section */
.featured-products {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

/* Enhanced Mobile-First Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
    padding: 0 16px;
}

/* Small phones (375px+) - Better spacing */
@media (min-width: 375px) {
    .product-grid {
        padding: 0 20px;
        gap: 24px;
    }
}

/* Medium phones (480px+) - Improved layout */
@media (min-width: 480px) {
    .product-grid {
        gap: 28px;
    }
}

/* Large phones/small tablets (640px+) - Two columns */
@media (min-width: 640px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
        padding: 0;
    }
}

/* Tablets (768px+) - Better spacing */
@media (min-width: 768px) {
    .product-grid {
        gap: 36px;
    }
}

/* Desktop (1024px+) - Three columns */
@media (min-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
    }
}

/* Large desktop (1200px+) - Optimal spacing */
@media (min-width: 1200px) {
    .product-grid {
        gap: 48px;
    }
}

/* Ultra-Modern 2025 Product Card with Glass Morphism */
.product-card.modern-card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.9) 100%);
    border-radius: 12px; /* 2025 modern radius */
    overflow: hidden;
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 4px 12px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.6); /* Glass morphism */
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 2025 smooth easing */
    position: relative;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2); /* Glass border */
    display: flex;
    flex-direction: column;
    /* Compact 4:3 aspect ratio base */
    aspect-ratio: 4/3;
    width: 100%;
    height: auto;
    min-height: 330px; /* Increased by 18% from 280px */
    max-height: 330px; /* Start collapsed - increased by 18% */
    overflow: hidden;
    backdrop-filter: blur(10px) saturate(1.2); /* Enhanced glass effect */
    -webkit-backdrop-filter: blur(10px) saturate(1.2);
}

/* Modern expanded state */
.product-card.modern-card.expanded {
    max-height: none;
    aspect-ratio: unset;
    min-height: 531px; /* Increased by 18% from 450px */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.08),
        0 8px 24px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Modern mobile touch feedback */
.product-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

/* Ultra-Modern 2025 Desktop Hover Effects */
@media (min-width: 768px) {
    .product-card.modern-card:hover {
        transform: translateY(-8px) scale(1.02); /* 2025 dynamic lift */
        box-shadow:
            0 4px 16px rgba(0, 0, 0, 0.08),
            0 12px 40px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .product-card.modern-card:active {
        transform: translateY(-6px) scale(1.01); /* Subtle press feedback */
    }

    /* 2025 Micro-interactions */
    .product-card.modern-card:hover .modern-title {
        color: var(--primary-green);
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .product-card.modern-card:hover .modern-brand {
        background: linear-gradient(135deg, rgba(46, 125, 50, 0.15) 0%, rgba(46, 125, 50, 0.08) 100%);
        border-color: rgba(46, 125, 50, 0.25);
        transform: scale(1.05);
    }

    .product-card.modern-card:hover .card-overlay {
        opacity: 0.1;
    }
}

/* 2025 Modern Mobile Optimizations */
@media (max-width: 767px) {
    .product-card.modern-card {
        min-height: 260px; /* Increased by 18% from 220px */
        max-height: 260px; /* Collapsed state - increased by 18% */
        border-radius: 10px; /* Modern radius for mobile */
    }

    .product-card.modern-card.expanded {
        min-height: 401px; /* Increased by 18% from 340px */
        max-height: none;
    }

    .product-image {
        height: 130px; /* Increased by 18% from 110px */
    }

    .product-info {
        padding: 10px; /* Less padding on mobile */
    }

    .product-actions {
        gap: 3px; /* Tighter spacing on mobile */
    }

    .btn-small, .btn-compact {
        font-size: 11px; /* Smaller text on mobile */
        padding: 6px 8px;
        min-height: 44px; /* Maintain touch target */
    }

    .product-title {
        font-size: 12px; /* Modern smaller title on mobile */
        min-height: 28px;
        margin-bottom: 2px;
        letter-spacing: -0.015em; /* Tighter modern spacing */
    }

    .product-brand {
        font-size: 11px;
        padding: 2px 4px;
        margin-bottom: 2px;
        letter-spacing: 0.6px; /* Adjusted for mobile */
    }

    .show-more-btn {
        font-size: 11px;
        padding: 4px 6px;
        margin: 3px 0;
        letter-spacing: 0.015em; /* Modern mobile spacing */
    }

    /* Price elements removed - 2025 quote-based design */

    .product-features li {
        font-size: 11px;
        margin-bottom: 1px;
        gap: 2px;
    }

    .product-features li i {
        font-size: 8px;
    }
}

/* 2025 Ultra-Modern Very Small Mobile Screens */
@media (max-width: 480px) {
    .product-card.modern-card {
        min-height: 236px; /* Increased by 18% from 200px */
        max-height: 236px; /* Increased by 18% */
        border-radius: 8px; /* Modern smaller radius */
    }

    .product-card.modern-card.expanded {
        min-height: 378px; /* Increased by 18% from 320px */
    }

    .product-image {
        height: 106px; /* Increased by 18% from 90px */
    }

    .product-info {
        padding: 8px;
    }

    .product-title {
        font-size: 11px; /* Ultra-compact for very small screens */
        min-height: 24px;
        margin-bottom: 1px;
        letter-spacing: -0.02em; /* Very tight modern spacing */
    }

    .product-brand {
        font-size: 11px;
        padding: 1px 3px;
        letter-spacing: 0.5px;
    }

    /* Price elements removed - 2025 quote-based design */

    .show-more-btn {
        font-size: 11px;
        padding: 3px 5px;
        margin: 2px 0;
        letter-spacing: 0.01em;
    }

    .warranty-info {
        font-size: 11px;
        padding: 2px 4px;
        gap: 2px;
    }

    .warranty-info i {
        font-size: 9px;
    }

    .product-actions-compact {
        flex-direction: column;
        gap: 4px;
    }

    .product-actions-compact .btn-compact {
        width: 100%;
        justify-content: center;
        font-size: 11px;
        padding: 4px 5px;
        letter-spacing: 0.005em; /* Modern button spacing */
    }
}

/* Ultra-Modern 2025 Product Image Container */
.product-image {
    position: relative;
    overflow: hidden;
    height: 165px; /* Increased by 18% from 140px */
    background: linear-gradient(135deg,
        rgba(248, 255, 254, 0.8) 0%,
        rgba(255, 255, 255, 0.9) 100%);
    flex-shrink: 0; /* Prevent image from shrinking */
    border-radius: 0; /* Clean modern edges */
}

/* 2025 Card Overlay Effect */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.02) 0%,
        rgba(255, 107, 53, 0.01) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: brightness(1.02) contrast(1.05); /* 2025 image enhancement */
}

.product-card.modern-card:hover .product-image img {
    transform: scale(1.08) rotate(0.5deg); /* 2025 dynamic zoom with subtle rotation */
    filter: brightness(1.05) contrast(1.08);
}

/* Enhanced image loading state */
.product-image img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease-in-out, transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-image img[loading="lazy"].loaded {
    opacity: 1;
}

.product-image img[loading="lazy"].loading {
    opacity: 0.3;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 2025 Ultra-Modern Product Badge */
.product-badge.modern-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg,
        var(--accent-orange) 0%,
        #ff6b35 50%,
        #ff5722 100%);
    color: var(--white);
    padding: 3px 8px;
    border-radius: 8px; /* 2025 refined radius */
    font-size: 11px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 15;
    box-shadow:
        0 1px 3px rgba(255, 107, 53, 0.3),
        0 2px 8px rgba(255, 107, 53, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Glass highlight */
    backdrop-filter: blur(12px) saturate(1.5);
    -webkit-backdrop-filter: blur(12px) saturate(1.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
}

.product-card.modern-card:hover .modern-badge {
    transform: scale(1.1) translateY(-1px);
    box-shadow:
        0 2px 6px rgba(255, 107, 53, 0.4),
        0 4px 12px rgba(255, 107, 53, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 2025 Modern Product Info Container */
.product-info {
    padding: 16px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    gap: 8px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
}

/* 2025 Modern Compact Content Area */
.product-info-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 2025 Modern Expandable Content Area */
.product-info-expandable.modern-expandable {
    display: none;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    animation: modernExpand 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.02) 100%);
    border-radius: 8px;
    padding: 12px;
    margin: 8px -4px 0 -4px;
}

.product-card.expanded .product-info-expandable {
    display: block;
}

/* 2025 Modern Expand Animation */
@keyframes modernExpand {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
        max-height: 0;
        filter: blur(2px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        max-height: 800px;
        filter: blur(0);
    }
}

/* Legacy animation for compatibility */
@keyframes expandContent {
    from {
        opacity: 0;
        transform: translateY(-15px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 600px;
    }
}

/* Card transition improvements */
.product-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card.expanded {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth height transitions */
.product-info-expandable {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top;
}

/* Loading state for expanding cards */
.product-card.expanding {
    pointer-events: none;
}

.product-card.expanding .show-more-btn {
    opacity: 0.7;
}

/* 2025 Ultra-Modern Product Title */
.product-title.modern-title {
    font-size: 14px;
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: 4px;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 34px;
    letter-spacing: -0.02em; /* 2025 ultra-tight spacing */
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 2025 system fonts */
}

/* 2025 Ultra-Modern Product Brand */
.product-brand.modern-brand {
    color: var(--primary-green);
    font-size: 11px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px; /* 2025 enhanced spacing */
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.1) 0%,
        rgba(46, 125, 50, 0.05) 50%,
        rgba(46, 125, 50, 0.02) 100%);
    padding: 3px 8px;
    border-radius: 8px;
    border: 1px solid rgba(46, 125, 50, 0.15);
    display: inline-block;
    margin-bottom: 4px;
    align-self: flex-start;
    backdrop-filter: blur(8px) saturate(1.2);
    -webkit-backdrop-filter: blur(8px) saturate(1.2);
    transition: all 0.2s ease;
    box-shadow:
        0 1px 3px rgba(46, 125, 50, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Modern compact product description (hidden in compact state) */
.product-description {
    color: var(--neutral-medium);
    font-size: 11px;
    line-height: 1.25;
    margin-bottom: 6px;
    display: none; /* Hidden in compact state */
    font-weight: 400; /* Modern regular weight */
    letter-spacing: -0.005em; /* Subtle modern spacing */
}

.product-card.expanded .product-description {
    display: block;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Compact product features (hidden in compact state) */
.product-features {
    list-style: none;
    margin-bottom: 12px;
    padding: 0;
    display: none; /* Hidden in compact state */
}

.product-card.expanded .product-features {
    display: block;
}

/* Modern compact product features */
.product-features li {
    display: flex;
    align-items: flex-start;
    gap: 3px;
    color: var(--neutral-medium);
    font-size: 11px;
    margin-bottom: 2px;
    line-height: 1.15;
    font-weight: 400; /* Modern regular weight */
    letter-spacing: -0.005em;
}

.product-features li i {
    color: var(--success);
    font-size: 9px;
    margin-top: 1px;
    flex-shrink: 0;
    opacity: 0.9; /* Modern subtle icon */
}

/* 2025 Ultra-Modern Show More/Less Button */
.show-more-btn.modern-expand {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.6) 100%);
    border: 1px solid rgba(46, 125, 50, 0.2);
    color: var(--primary-green);
    font-size: 11px;
    font-weight: 700;
    cursor: pointer;
    padding: 8px 12px;
    margin: 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: 44px;
    border-radius: 8px;
    -webkit-tap-highlight-color: rgba(46, 125, 50, 0.1);
    width: 100%;
    text-decoration: none;
    letter-spacing: 0.03em;
    backdrop-filter: blur(8px) saturate(1.2);
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.show-more-btn:hover,
.show-more-btn:focus {
    color: var(--white);
    background: linear-gradient(135deg, var(--primary-green) 0%, #1e5e23 100%);
    border-color: var(--primary-green);
    outline: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.show-more-btn:active {
    transform: scale(0.98);
}

.show-more-btn i {
    font-size: 9px;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.85; /* Modern subtle icon */
}

.show-more-btn.expanded i {
    transform: rotate(180deg);
}

/* Focus styles for accessibility */
.show-more-btn:focus-visible {
    outline: 2px solid var(--accent-orange);
    outline-offset: 2px;
}

/* Additional product details (hidden by default) */
.product-details-extra {
    display: none;
    margin-bottom: 12px;
    overflow: hidden;
}

.product-details-extra.visible {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* Cross-browser animation support */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
        -webkit-transform: translateY(-10px);
        -moz-transform: translateY(-10px);
        -ms-transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
        -webkit-transform: translateY(0);
        -moz-transform: translateY(0);
        -ms-transform: translateY(0);
    }
}

/* Webkit-specific animation */
@-webkit-keyframes fadeIn {
    from { opacity: 0; -webkit-transform: translateY(-10px); }
    to { opacity: 1; -webkit-transform: translateY(0); }
}

/* Mozilla-specific animation */
@-moz-keyframes fadeIn {
    from { opacity: 0; -moz-transform: translateY(-10px); }
    to { opacity: 1; -moz-transform: translateY(0); }
}

/* 2025 Modern Product Actions */
.product-actions-compact {
    display: flex;
    gap: 8px;
    margin-top: auto;
    padding-top: 8px;
}

/* 2025 Modern Secondary Button */
.btn-secondary.modern-secondary {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%);
    color: var(--primary-green);
    border: 1px solid rgba(46, 125, 50, 0.2);
    border-radius: 8px;
    padding: 10px 14px;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(8px) saturate(1.1);
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.btn-secondary.modern-secondary:hover {
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.1) 0%,
        rgba(46, 125, 50, 0.05) 100%);
    border-color: rgba(46, 125, 50, 0.3);
    transform: translateY(-1px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Modern Full Actions for Expanded State */
.product-actions-full.modern-actions {
    display: none;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.product-card.expanded .modern-actions {
    display: flex;
    flex-direction: column;
}

/* Modern compact button styling */
.btn-small {
    padding: 6px 8px;
    font-size: 11px;
    min-height: 44px; /* Ensure 44px minimum touch target */
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    text-decoration: none;
    border: 2px solid transparent;
    flex: 1; /* Equal width buttons in compact state */
    letter-spacing: 0.01em; /* Modern button spacing */
    backdrop-filter: blur(1px); /* Subtle modern effect */
}

/* Modern compact button for collapsed state */
.btn-compact {
    padding: 5px 6px;
    font-size: 11px;
    min-height: 44px;
    border-radius: 4px;
    font-weight: 600;
    flex: 1;
    letter-spacing: 0.01em;
}

/* Enhanced button styles for mobile */
.btn-small:active {
    transform: scale(0.98);
}

.btn-small.btn-primary {
    background: linear-gradient(135deg, var(--primary-green) 0%, #1e5e23 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
}

.btn-small.btn-primary:hover {
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
    transform: translateY(-1px);
}

.btn-small.btn-secondary {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #e55a2b 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.btn-small.btn-secondary:hover {
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
    transform: translateY(-1px);
}

.btn-small.btn-outline {
    background: white;
    color: var(--primary-green);
    border-color: var(--primary-green);
    min-width: 44px;
    padding: 12px;
}

.btn-small.btn-outline:hover {
    background: var(--primary-green);
    color: white;
}

/* Enhanced Product Card Styles */
.image-gallery {
    position: relative;
    height: 100%;
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
    background-color: var(--neutral-light);
    background-image: url("data:image/svg+xml,%3Csvg width='400' height='300' viewBox='0 0 400 300' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Crect x='150' y='100' width='100' height='100' fill='%23d1d5db' rx='10'/%3E%3Ccircle cx='175' cy='125' r='15' fill='%239ca3af'/%3E%3Cpath d='M160 160 L190 130 L210 150 L190 170 Z' fill='%239ca3af'/%3E%3Ctext x='200' y='220' text-anchor='middle' font-family='Arial, sans-serif' font-size='16' fill='%236b7280'%3EProduct Image%3C/text%3E%3C/svg%3E");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.image-thumbnails {
    position: absolute;
    bottom: var(--spacing-sm);
    left: var(--spacing-sm);
    right: var(--spacing-sm);
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.product-card:hover .image-thumbnails {
    opacity: 1;
}

.thumbnail {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    border: 2px solid transparent;
    cursor: pointer;
    transition: var(--transition-fast);
}

.thumbnail.active,
.thumbnail:hover {
    border-color: var(--white);
    box-shadow: 0 0 0 2px var(--primary-green);
}

.more-images {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--white);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.more-images:hover {
    background-color: var(--primary-green);
}

.certifications {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.cert-badge {
    background-color: rgba(255, 255, 255, 0.95);
    color: var(--primary-green);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.product-rating {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: #ffc107;
    font-size: var(--font-size-sm);
}

.rating-text {
    font-size: var(--font-size-xs);
    color: var(--neutral-medium);
}

/* PRICE ELEMENTS REMOVED - 2025 DESIGN FOCUSES ON QUOTE-BASED INTERACTION */

/* 2025 Modern Primary CTA Button */
.btn-quote.modern-cta {
    background: linear-gradient(135deg,
        var(--primary-green) 0%,
        #1e5e23 50%,
        #0d4f12 100%);
    color: var(--white);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
        0 2px 8px rgba(46, 125, 50, 0.3),
        0 4px 16px rgba(46, 125, 50, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px);
    position: relative;
    overflow: hidden;
}

.btn-quote.modern-cta:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 4px 12px rgba(46, 125, 50, 0.4),
        0 8px 24px rgba(46, 125, 50, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-quote.modern-cta:active {
    transform: translateY(-1px) scale(1.01);
}

/* 2025 Modern Expanded Content Sections */
.section-title {
    font-size: 12px;
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
}

.section-title i {
    color: var(--primary-green);
    font-size: 11px;
    opacity: 0.8;
}

/* Modern Features Section */
.features-section {
    margin-bottom: 12px;
}

.product-features.modern-features {
    list-style: none;
    margin: 0;
    padding: 0;
    display: grid;
    gap: 4px;
}

.product-features.modern-features li {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    color: var(--neutral-medium);
    font-size: 11px;
    line-height: 1.3;
    font-weight: 400;
    padding: 4px 0;
}

.product-features.modern-features li i {
    color: var(--success);
    font-size: 10px;
    margin-top: 2px;
    flex-shrink: 0;
    opacity: 0.9;
}

.product-features.modern-features li span {
    flex: 1;
}

/* Modern Specifications Grid */
.specs-section {
    margin-bottom: 12px;
}

.specs-grid {
    display: grid;
    gap: 6px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(255, 255, 255, 0.3) 100%);
    border-radius: 6px;
    border: 1px solid rgba(46, 125, 50, 0.08);
}

.spec-label {
    font-size: 11px;
    color: var(--neutral-medium);
    font-weight: 500;
}

.spec-value {
    font-size: 11px;
    color: var(--neutral-dark);
    font-weight: 600;
}

/* Modern Certifications Grid */
.certifications-section {
    margin-bottom: 12px;
}

.certifications-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.cert-badge.modern-cert {
    background: linear-gradient(135deg,
        rgba(46, 125, 50, 0.1) 0%,
        rgba(46, 125, 50, 0.05) 100%);
    color: var(--primary-green);
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid rgba(46, 125, 50, 0.15);
    backdrop-filter: blur(4px);
}

/* Modern Rating Section */
.product-rating-section.modern-rating {
    margin-bottom: 12px;
}

.rating-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.1) 0%,
        rgba(255, 193, 7, 0.05) 100%);
    border-radius: 6px;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

/* Modern Description */
.product-description.modern-description {
    font-size: 11px;
    line-height: 1.4;
    color: var(--neutral-medium);
    margin-bottom: 12px;
    padding: 8px 10px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.2) 100%);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Modern Warranty Section */
.warranty-section {
    margin-bottom: 12px;
}

.warranty-info.modern-warranty {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.1) 0%,
        rgba(34, 197, 94, 0.05) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 6px;
    padding: 6px 10px;
    backdrop-filter: blur(4px);
}

/* Modern compact warranty info styling */
.warranty-info {
    display: flex;
    align-items: center;
    gap: 3px;
    margin-bottom: 4px;
    padding: 3px 5px;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(34, 197, 94, 0.03) 100%);
    border-radius: 4px;
    color: var(--success);
    font-size: 11px;
    font-weight: 500;
    border: 1px solid rgba(34, 197, 94, 0.12);
    letter-spacing: 0.005em;
    backdrop-filter: blur(1px); /* Modern glass effect */
}

.warranty-info i {
    color: var(--success);
    font-size: 10px;
    opacity: 0.9; /* Modern subtle icon */
}

.product-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.product-actions .btn {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.product-actions .btn-outline {
    flex: 0 0 auto;
    width: 40px;
    padding: var(--spacing-sm);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    z-index: 1000;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-warning {
    border-left: 4px solid var(--warning);
}

.notification-info {
    border-left: 4px solid var(--info);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-content i {
    font-size: var(--font-size-lg);
}

.notification-success .notification-content i {
    color: var(--success);
}

.notification-warning .notification-content i {
    color: var(--warning);
}

.notification-info .notification-content i {
    color: var(--info);
}

.notification-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--neutral-medium);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.notification-close:hover {
    color: var(--neutral-dark);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.section-cta {
    text-align: center;
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-3xl) 0;
}

.testimonial-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 768px) {
    .testimonial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .testimonial-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.testimonial-card {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    position: relative;
    transition: all var(--transition-normal);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: var(--spacing-lg);
    font-size: 4rem;
    color: var(--primary-green);
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
}

.testimonial-content p {
    font-style: italic;
    color: var(--neutral-dark);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.author-info span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

/* Hero Section Enhancements */
.hero-features {
    display: flex;
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    flex-wrap: wrap;
}

.hero-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background-color: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.hero-feature i {
    color: var(--accent-orange);
    font-size: var(--font-size-lg);
}

.hero-equipment-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.equipment-card {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.equipment-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.equipment-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.equipment-card span {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-align: center;
}

/* Trust Bar Enhancements */
.trust-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

@media (min-width: 768px) {
    .trust-content {
        grid-template-columns: 1fr 1fr;
    }
}

.trust-stats {
    display: flex;
    justify-content: space-around;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-medium);
    font-weight: 500;
}

.trust-certifications {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    justify-content: center;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background-color: var(--light-blue);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 2px solid var(--primary-green);
    color: var(--primary-green);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.cert-item i {
    color: var(--primary-green);
}

/* Category Card Enhancements */
.category-features {
    display: flex;
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
    flex-wrap: wrap;
}

.category-features span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--success);
    font-weight: 500;
}

.category-features i {
    font-size: var(--font-size-xs);
}

/* Testimonial Enhancements */
.author-avatar {
    width: 50px;
    height: 50px;
    background-color: var(--primary-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
}

.author-rating {
    display: flex;
    gap: 2px;
    margin-top: var(--spacing-xs);
}

.author-rating i {
    color: #ffc107;
    font-size: var(--font-size-sm);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    padding: var(--spacing-3xl) 0;
    text-align: center;
    color: var(--white);
}

.cta-content h2 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.cta-feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.9);
}

.cta-feature i {
    font-size: var(--font-size-2xl);
    color: var(--accent-orange);
}

.cta-feature span {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.cta-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

@media (min-width: 640px) {
    .cta-buttons {
        flex-direction: row;
        justify-content: center;
    }
}

.cta-buttons .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cta-buttons .btn-secondary {
    background-color: var(--white);
    color: var(--primary-green);
    border-color: var(--white);
}

.cta-buttons .btn-secondary:hover {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

.cta-buttons .btn-outline {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

.cta-buttons .btn-outline:hover {
    background-color: var(--white);
    color: var(--primary-green);
}

/* Mobile Responsiveness Improvements */
@media (max-width: 768px) {
    .hero-features {
        justify-content: center;
    }

    .hero-feature {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }

    .hero-equipment-showcase {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .trust-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .trust-certifications {
        justify-content: center;
    }

    .cert-item {
        flex: 1;
        min-width: 140px;
        justify-content: center;
    }

    .category-features {
        justify-content: center;
    }

    .cta-features {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .cta-buttons {
        width: 100%;
    }

    .cta-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-features {
        flex-direction: column;
        align-items: center;
    }

    .hero-feature {
        width: 100%;
        max-width: 200px;
    }

    .trust-certifications {
        flex-direction: column;
        align-items: center;
    }

    .cert-item {
        width: 100%;
        max-width: 200px;
    }

    .cta-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* Footer */
.footer {
    background-color: var(--neutral-dark);
    color: var(--white);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

.footer-section h4 {
    color: var(--white);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
    padding-left: var(--spacing-sm);
}

.footer-brand .logo {
    margin-bottom: var(--spacing-lg);
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-sm);
}

.contact-info i {
    color: var(--accent-orange);
    width: 20px;
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-links a:hover {
    background-color: var(--accent-orange);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

.footer-bottom a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.footer-bottom a:hover {
    color: var(--white);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background-color: #25D366;
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: all var(--transition-normal);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    background-color: #128C7E;
    transform: scale(1.1);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    padding: var(--spacing-3xl) 0 var(--spacing-2xl);
    color: var(--white);
    text-align: center;
}

.page-header-content h1 {
    color: var(--white);
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
}

.page-header-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--white);
}

.breadcrumb span {
    color: rgba(255, 255, 255, 0.6);
}

/* Company Story Section */
.company-story {
    padding: var(--spacing-3xl) 0;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

@media (min-width: 1024px) {
    .story-content {
        grid-template-columns: 2fr 1fr;
    }
}

.story-text h2 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-lg);
}

.lead {
    font-size: var(--font-size-xl);
    font-weight: 500;
    color: var(--neutral-dark);
    line-height: 1.5;
    margin-bottom: var(--spacing-xl);
}

.story-text p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.story-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

@media (min-width: 768px) {
    .story-stats {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: var(--light-blue);
    border-radius: var(--radius-lg);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Mission & Vision Section */
.mission-vision {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 768px) {
    .mv-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .mv-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.mv-card {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.mv-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.mv-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-orange), #FF8A50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.mv-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.mv-card h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.mv-card p {
    line-height: 1.6;
}

.mv-card ul {
    list-style: none;
    text-align: left;
    margin-top: var(--spacing-md);
}

.mv-card ul li {
    margin-bottom: var(--spacing-sm);
    color: var(--neutral-medium);
}

.mv-card ul li strong {
    color: var(--neutral-dark);
}

/* Team Section */
.team-section {
    padding: var(--spacing-3xl) 0;
}

.team-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.team-member {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-align: center;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.member-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: var(--spacing-xl);
}

.member-info h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.member-role {
    color: var(--primary-green);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-md);
    display: block;
}

.member-info p {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
}

.member-social {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.member-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background-color: var(--neutral-light);
    color: var(--neutral-medium);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-normal);
}

.member-social a:hover {
    background-color: var(--primary-green);
    color: var(--white);
    transform: translateY(-2px);
}

/* Credentials Section */
.credentials {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

.credentials-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .credentials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .credentials-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.credential-card {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.credential-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-green);
}

.credential-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--dark-blue), var(--primary-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: var(--transition-normal);
}

.credential-card:hover .credential-icon {
    transform: scale(1.1);
}

.credential-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.credential-card h4 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.credential-card p {
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: var(--spacing-3xl) 0;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.feature-card {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(46, 125, 50, 0.05), transparent);
    transition: var(--transition-slow);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--secondary-green), var(--primary-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: var(--transition-normal);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.feature-card h4 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--neutral-medium);
    line-height: 1.6;
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-sm);
}

/* Mobile-First Form Elements */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-md);
    font-size: var(--mobile-font-base);
    font-family: inherit;
    transition: var(--transition-fast);
    background-color: var(--white);
    min-height: var(--touch-target-min);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    touch-action: manipulation;
}

/* Desktop form adjustments */
@media (min-width: 768px) {
    .form-input,
    .form-select,
    .form-textarea {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--error);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    color: var(--error);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.error-message::before {
    content: '⚠';
    font-size: var(--font-size-sm);
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background-color: var(--neutral-dark);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    transition: var(--transition-fast);
    pointer-events: none;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--neutral-dark);
}

.tooltip.show {
    opacity: 1;
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Responsive Utilities */
@media (max-width: 767px) {
    .hide-mobile {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .hide-desktop {
        display: none !important;
    }
}

/* Contact Page Styles */
.contact-methods {
    padding: var(--spacing-3xl) 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 768px) {
    .contact-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.contact-card {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: var(--transition-normal);
}

.contact-icon.whatsapp {
    background: linear-gradient(135deg, #25D366, #128C7E);
}

.contact-card:hover .contact-icon {
    transform: scale(1.1);
}

.contact-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.contact-card h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.contact-card p {
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-lg);
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-link {
    color: var(--accent-orange);
    text-decoration: none;
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
}

.contact-link:hover {
    color: #E55A2B;
}

.contact-hours {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

/* Contact Form Section */
.contact-form-section {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
}

@media (min-width: 1024px) {
    .contact-content {
        grid-template-columns: 2fr 1fr;
    }
}

.form-container {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.form-header h2 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.form-header p {
    color: var(--neutral-medium);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 640px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    opacity: 0;
    position: absolute;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: bold;
}

/* Contact Info Sidebar */
.contact-info-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.info-card {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.info-card h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.promise-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.promise-item:last-child {
    margin-bottom: 0;
}

.promise-item i {
    width: 40px;
    height: 40px;
    background-color: var(--light-blue);
    color: var(--primary-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.promise-item div {
    display: flex;
    flex-direction: column;
}

.promise-item strong {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.promise-item span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.process-step {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.process-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: var(--accent-orange);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.step-content {
    display: flex;
    flex-direction: column;
}

.step-content strong {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-xs);
}

.step-content span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.address-info {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.address-info i {
    color: var(--accent-orange);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-xs);
}

.address-info div {
    display: flex;
    flex-direction: column;
}

.address-info strong {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-sm);
}

.address-info span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-3xl) 0;
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-normal);
}

.faq-item.active {
    border-color: var(--primary-green);
}

.faq-question {
    padding: var(--spacing-lg);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
}

.faq-question:hover {
    background-color: var(--light-blue);
}

.faq-question h4 {
    color: var(--neutral-dark);
    margin: 0;
    font-size: var(--font-size-base);
}

.faq-question i {
    color: var(--primary-green);
    transition: var(--transition-fast);
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 var(--spacing-lg);
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item.active .faq-answer {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    max-height: 200px;
}

.faq-answer p {
    color: var(--neutral-medium);
    line-height: 1.6;
    margin: 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--neutral-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--neutral-dark);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--neutral-medium);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.modal-close:hover {
    color: var(--neutral-dark);
}

.modal-body {
    padding: var(--spacing-xl);
    text-align: center;
}

.success-icon {
    margin-bottom: var(--spacing-lg);
}

.success-icon i {
    font-size: 4rem;
    color: var(--success);
}

.modal-body p {
    margin-bottom: var(--spacing-md);
}

.modal-body ul {
    text-align: left;
    margin: var(--spacing-lg) 0;
    padding-left: var(--spacing-lg);
}

.modal-body ul li {
    margin-bottom: var(--spacing-sm);
    color: var(--neutral-medium);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--neutral-light);
    text-align: center;
}

/* Products Page Styles */
.products-section {
    padding: var(--spacing-2xl) 0;
}

.products-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
}

@media (min-width: 1024px) {
    .products-layout {
        grid-template-columns: 280px 1fr;
    }
}

/* Filters Sidebar */
.filters-sidebar {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--neutral-light);
}

.filters-header h3 {
    color: var(--neutral-dark);
    margin: 0;
}

.clear-filters {
    background: none;
    border: none;
    color: var(--accent-orange);
    font-size: var(--font-size-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: var(--transition-fast);
}

.clear-filters:hover {
    color: #E55A2B;
}

.filter-section {
    margin-bottom: var(--spacing-xl);
}

.filter-section h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--neutral-light);
}

.search-filter {
    position: relative;
}

.search-filter input {
    padding-right: 40px;
}

.search-filter i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--neutral-medium);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.filter-option:hover {
    background-color: var(--light-blue);
}

.filter-option input[type="checkbox"] {
    display: none;
}

.filter-option .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.filter-option input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: bold;
}

.filter-label {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--neutral-dark);
}

.filter-count {
    font-size: var(--font-size-xs);
    color: var(--neutral-medium);
}

/* Products Content */
.products-content {
    min-height: 600px;
}

.active-filters {
    display: none;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--light-blue);
    border-radius: var(--radius-lg);
    border: 1px solid var(--primary-green);
}

.active-filters-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
}

.active-filters-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--neutral-dark);
    margin-right: var(--spacing-sm);
}

.filter-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--primary-green);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.filter-tag .remove-filter {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: 0;
    margin-left: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.filter-tag .remove-filter:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.clear-all-filters {
    background-color: var(--accent-orange);
    color: var(--white);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.clear-all-filters:hover {
    background-color: #e55a00;
}

/* No Products State */
.no-products {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--neutral-medium);
}

.no-products i {
    font-size: 4rem;
    color: var(--neutral-light);
    margin-bottom: var(--spacing-lg);
}

.no-products h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.no-products p {
    margin-bottom: var(--spacing-lg);
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.results-info {
    flex: 1;
}

.result-count {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-options label {
    color: var(--neutral-dark);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.sort-options select {
    min-width: 150px;
}

.view-options {
    display: flex;
    gap: var(--spacing-xs);
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 2px solid var(--neutral-light);
    background-color: var(--white);
    color: var(--neutral-medium);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.view-btn:hover,
.view-btn.active {
    border-color: var(--primary-green);
    color: var(--primary-green);
    background-color: var(--light-blue);
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 640px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Products List View */
.products-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.products-list .product-card {
    display: grid;
    grid-template-columns: 200px 1fr auto;
    gap: var(--spacing-lg);
    align-items: center;
}

.products-list .product-image {
    height: 177px; /* Increased by 18% from 150px */
}

.products-list .product-info {
    padding: var(--spacing-md) 0;
}

.products-list .product-actions {
    flex-direction: column;
    align-items: stretch;
    min-width: 150px;
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-2xl);
}

/* Comparison Bar */
.comparison-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-top: 2px solid var(--primary-green);
    box-shadow: var(--shadow-lg);
    z-index: 100;
    transform: translateY(100%);
    transition: var(--transition-normal);
}

.comparison-bar.active {
    transform: translateY(0);
}

.comparison-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) 0;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.comparison-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.comparison-count {
    background-color: var(--primary-green);
    color: var(--white);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.comparison-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Mobile Filters */
@media (max-width: 1023px) {
    .filters-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 300px;
        height: 100vh;
        z-index: 1000;
        overflow-y: auto;
        transition: var(--transition-normal);
    }

    .filters-sidebar.active {
        left: 0;
    }

    .filters-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }

    .filters-overlay.active {
        display: block;
    }

    .mobile-filters-toggle {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background-color: var(--primary-green);
        color: var(--white);
        border: none;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-md);
        cursor: pointer;
        margin-bottom: var(--spacing-lg);
        transition: var(--transition-fast);
    }

    .mobile-filters-toggle:hover {
        background-color: #1E5E22;
    }
}

@media (min-width: 1024px) {
    .mobile-filters-toggle {
        display: none;
    }
}

/* Product Detail Page Styles */
.breadcrumb-section {
    background-color: var(--neutral-light);
    padding: var(--spacing-md) 0;
}

.breadcrumb-section .breadcrumb {
    justify-content: flex-start;
}

.product-detail-section {
    padding: var(--spacing-2xl) 0;
}

.product-detail-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
}

@media (min-width: 1024px) {
    .product-detail-layout {
        grid-template-columns: 1fr 1fr;
    }
}

/* Product Images */
.product-images {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.main-image {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background-color: var(--neutral-light);
    aspect-ratio: 4/3;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-badges {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
}

.video-play-btn {
    position: absolute;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.video-play-btn:hover {
    background-color: var(--accent-orange);
}

.thumbnail-images {
    display: flex;
    gap: var(--spacing-sm);
    overflow-x: auto;
    padding: var(--spacing-sm) 0;
}

.thumbnail-images img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.thumbnail-images img:hover,
.thumbnail-images img.active {
    border-color: var(--primary-green);
}

/* Product Info */
.product-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.product-header {
    border-bottom: 1px solid var(--neutral-light);
    padding-bottom: var(--spacing-lg);
}

.product-brand {
    color: var(--primary-green);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
}

.product-title {
    color: var(--neutral-dark);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stars {
    display: flex;
    gap: var(--spacing-xs);
}

.stars i {
    color: var(--warning);
    font-size: var(--font-size-sm);
}

.rating-text {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.product-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-orange);
}

.product-description {
    color: var(--neutral-medium);
    line-height: 1.7;
    font-size: var(--font-size-lg);
}

.product-features h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.product-features ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.product-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--neutral-medium);
}

.product-features li i {
    color: var(--success);
    font-size: var(--font-size-sm);
    width: 16px;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    background-color: var(--light-blue);
    border-radius: var(--radius-lg);
}

@media (min-width: 640px) {
    .product-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }
}

.product-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.meta-item i {
    color: var(--primary-green);
    width: 20px;
}

/* Product Tabs */
.product-tabs-section {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

.tabs-container {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.tabs-nav {
    display: flex;
    background-color: var(--neutral-light);
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: none;
    border: none;
    color: var(--neutral-medium);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover,
.tab-btn.active {
    color: var(--primary-green);
    background-color: var(--white);
    border-bottom-color: var(--primary-green);
}

.tabs-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
    padding: var(--spacing-2xl);
}

.tab-panel.active {
    display: block;
}

.tab-content h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-lg);
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-lg);
}

.specs-table tr {
    border-bottom: 1px solid var(--neutral-light);
}

.specs-table td {
    padding: var(--spacing-md);
    vertical-align: top;
}

.specs-table td:first-child {
    font-weight: 600;
    color: var(--neutral-dark);
    width: 40%;
}

.specs-table td:last-child {
    color: var(--neutral-medium);
}

/* Reviews */
.reviews-summary {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background-color: var(--light-blue);
    border-radius: var(--radius-lg);
}

.rating-overview {
    text-align: center;
}

.overall-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.rating-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-green);
}

.rating-stars {
    display: flex;
    gap: var(--spacing-xs);
}

.rating-stars i {
    color: var(--warning);
    font-size: var(--font-size-lg);
}

.total-reviews {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

/* Financing Options */
.financing-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
}

@media (min-width: 768px) {
    .financing-options {
        grid-template-columns: repeat(3, 1fr);
    }
}

.financing-card {
    background-color: var(--white);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: var(--transition-normal);
}

.financing-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.financing-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--accent-orange), #FF8A50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.financing-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.financing-card h4 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.financing-card p {
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.financing-card ul {
    list-style: none;
    text-align: left;
}

.financing-card ul li {
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-sm);
    position: relative;
    padding-left: var(--spacing-lg);
}

.financing-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success);
    font-weight: bold;
}

/* Video Modal */
.video-modal .modal-content {
    max-width: 800px;
    width: 90%;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Related Products */
.related-products {
    padding: var(--spacing-3xl) 0;
}

/* Gallery Page Styles */
.gallery-nav {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--neutral-light);
    position: sticky;
    top: 80px;
    z-index: 100;
}

.gallery-filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: transparent;
    border: 2px solid var(--neutral-light);
    color: var(--neutral-medium);
    border-radius: var(--radius-xl);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
    color: var(--white);
    transform: translateY(-2px);
}

.photo-gallery-section {
    padding: var(--spacing-3xl) 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background-color: var(--neutral-light);
    aspect-ratio: 4/3;
    cursor: pointer;
    transition: var(--transition-normal);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.gallery-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    opacity: 0;
    transition: var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-view-btn {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    color: var(--primary-green);
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-view-btn:hover {
    background-color: var(--white);
    transform: scale(1.1);
}

.gallery-info {
    text-align: center;
    color: var(--white);
}

.gallery-info h4 {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
    color: var(--white);
}

.gallery-info p {
    font-size: var(--font-size-sm);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

/* Gallery Statistics */
.gallery-stats {
    padding: var(--spacing-3xl) 0;
    background-color: var(--neutral-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-card {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.stat-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Video Gallery Link */
.video-gallery-link {
    padding: var(--spacing-3xl) 0;
}

.video-link-card {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
    color: var(--white);
    flex-wrap: wrap;
}

.video-link-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.video-link-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.video-link-icon i {
    font-size: var(--font-size-3xl);
    color: var(--white);
}

.video-link-text h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.video-link-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

.video-link-card .btn {
    flex-shrink: 0;
}

.video-link-card .btn-primary {
    background-color: var(--white);
    color: var(--primary-green);
    border-color: var(--white);
}

.video-link-card .btn-primary:hover {
    background-color: var(--accent-orange);
    color: var(--white);
    border-color: var(--accent-orange);
}

/* Lightbox Modal */
.lightbox-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: var(--spacing-lg);
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: none;
    border: none;
    color: var(--white);
    font-size: var(--font-size-3xl);
    cursor: pointer;
    z-index: 2001;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.lightbox-close:hover {
    color: var(--accent-orange);
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: var(--white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    z-index: 2001;
}

.lightbox-prev {
    left: -70px;
}

.lightbox-next {
    right: -70px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background-color: var(--accent-orange);
}

#lightboxImage {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: var(--radius-md);
}

.lightbox-info {
    text-align: center;
    color: var(--white);
    margin-top: var(--spacing-lg);
    max-width: 600px;
}

.lightbox-info h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.lightbox-info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .video-link-card {
        flex-direction: column;
        text-align: center;
    }

    .video-link-content {
        flex-direction: column;
        text-align: center;
    }

    .lightbox-prev {
        left: 10px;
    }

    .lightbox-next {
        right: 10px;
    }

    .lightbox-close {
        top: 10px;
        right: 10px;
    }
}

/* Video Gallery Styles */
.video-gallery-section {
    padding: var(--spacing-3xl) 0;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.video-item {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.video-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.video-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    cursor: pointer;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.video-item:hover .video-thumbnail img {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.video-item:hover .video-overlay {
    opacity: 1;
}

.play-button {
    width: 70px;
    height: 70px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-green);
    font-size: var(--font-size-2xl);
    transition: var(--transition-fast);
}

.play-button:hover {
    background-color: var(--white);
    transform: scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: var(--spacing-sm);
    right: var(--spacing-sm);
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.video-info {
    padding: var(--spacing-lg);
}

.video-info h3 {
    color: var(--neutral-dark);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.video-info p {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-xs);
    color: var(--neutral-medium);
}

.video-views {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Photo Gallery Link (for video page) */
.photo-gallery-link {
    padding: var(--spacing-3xl) 0;
}

.photo-link-card {
    background: linear-gradient(135deg, var(--dark-blue), var(--primary-green));
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-xl);
    color: var(--white);
    flex-wrap: wrap;
}

.photo-link-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.photo-link-icon {
    width: 80px;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.photo-link-icon i {
    font-size: var(--font-size-3xl);
    color: var(--white);
}

.photo-link-text h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.photo-link-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

.photo-link-card .btn {
    flex-shrink: 0;
}

.photo-link-card .btn-primary {
    background-color: var(--white);
    color: var(--dark-blue);
    border-color: var(--white);
}

.photo-link-card .btn-primary:hover {
    background-color: var(--accent-orange);
    color: var(--white);
    border-color: var(--accent-orange);
}

/* Mobile Responsive for Video Gallery */
@media (max-width: 768px) {
    .video-grid {
        grid-template-columns: 1fr;
    }

    .photo-link-card {
        flex-direction: column;
        text-align: center;
    }

    .photo-link-content {
        flex-direction: column;
        text-align: center;
    }
}

/* Comparison Page Styles */
.comparison-section {
    padding: var(--spacing-3xl) 0;
}

.comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.comparison-info h2 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-sm);
}

.comparison-info p {
    color: var(--neutral-medium);
    margin: 0;
}

.comparison-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.product-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

.selection-slot {
    border: 2px dashed var(--neutral-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-normal);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selection-slot.empty:hover {
    border-color: var(--primary-green);
    background-color: var(--light-blue);
}

.selection-slot.filled {
    border-style: solid;
    border-color: var(--primary-green);
    background-color: var(--white);
    cursor: default;
}

.selection-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--neutral-medium);
}

.selection-placeholder i {
    font-size: var(--font-size-3xl);
    color: var(--neutral-light);
}

.selected-product {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
}

.remove-product {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background-color: var(--error);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.remove-product:hover {
    background-color: #D32F2F;
    transform: scale(1.1);
}

.selected-product img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--radius-sm);
}

.selected-product-info {
    text-align: center;
}

.selected-product-info h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.selected-product-info span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.comparison-table-container {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.comparison-table-element {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table-element th,
.comparison-table-element td {
    padding: var(--spacing-lg);
    text-align: left;
    border-bottom: 1px solid var(--neutral-light);
}

.spec-header {
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
    font-weight: 600;
    width: 200px;
    position: sticky;
    left: 0;
    z-index: 10;
}

.product-header {
    background-color: var(--light-blue);
    text-align: center;
    min-width: 250px;
}

.product-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.product-header-content img {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: var(--radius-sm);
}

.product-header-info h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.product-header-info span {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

.spec-name {
    background-color: var(--neutral-light);
    color: var(--neutral-dark);
    font-weight: 500;
    position: sticky;
    left: 0;
    z-index: 5;
}

.spec-value {
    color: var(--neutral-medium);
    text-align: center;
}

.comparison-actions-row {
    display: grid;
    grid-template-columns: 200px repeat(auto-fit, minmax(250px, 1fr));
    background-color: var(--light-blue);
}

.comparison-actions-row::before {
    content: '';
    grid-column: 1;
}

.product-actions {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.no-products-message {
    text-align: center;
    padding: var(--spacing-3xl);
    background-color: var(--neutral-light);
    border-radius: var(--radius-lg);
}

.no-products-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-products-content i {
    font-size: 4rem;
    color: var(--neutral-light);
    margin-bottom: var(--spacing-lg);
}

.no-products-content h3 {
    color: var(--neutral-dark);
    margin-bottom: var(--spacing-md);
}

.no-products-content p {
    color: var(--neutral-medium);
    margin-bottom: var(--spacing-xl);
}

/* Product Selector Modal */
.product-selector-modal .modal-content {
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
}

.product-search {
    margin-bottom: var(--spacing-lg);
}

.product-list {
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.product-list-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 2px solid var(--neutral-light);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.product-list-item:hover {
    border-color: var(--primary-green);
    background-color: var(--light-blue);
}

.product-list-item img {
    width: 60px;
    height: 45px;
    object-fit: cover;
    border-radius: var(--radius-sm);
    flex-shrink: 0;
}

.product-list-info {
    flex: 1;
}

.product-list-info h4 {
    color: var(--neutral-dark);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.product-list-info .product-brand {
    color: var(--neutral-medium);
    font-size: var(--font-size-sm);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .comparison-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-selection {
        grid-template-columns: 1fr;
    }

    .comparison-table-container {
        overflow-x: auto;
    }

    .comparison-table-element {
        min-width: 600px;
    }

    .comparison-actions-row {
        grid-template-columns: 200px repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .whatsapp-float,
    .cta-section {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .container {
        max-width: none;
        padding: 0;
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .whatsapp-float,
    .cta-section {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .container {
        max-width: none;
        padding: 0;
    }
}
